

<style>
.performance-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.page-header {
  background: rgba(255, 255, 255, 0.95);
  padding: 2rem;
  border-radius: 15px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.page-header h1 {
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.controls-section {
  background: rgba(255, 255, 255, 0.95);
  padding: 1.5rem;
  border-radius: 15px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  align-items: end;
}

.form-group {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-weight: 600;
  font-size: 0.9rem;
}

.form-control {
  width: 100%;
  padding: 0.5rem;
  border: 2px solid #ecf0f1;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
}

.analytics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.analytics-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-title {
  color: #2c3e50;
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.team-performance-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.team-performance-table th,
.team-performance-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #ecf0f1;
}

.team-performance-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
}

.team-performance-table tr:hover {
  background: #f8f9fa;
}

.metric-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: #2c3e50;
}

.metric-label {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-top: 0.25rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #27ae60, #2ecc71);
  transition: width 0.3s ease;
}

.progress-fill.warning {
  background: linear-gradient(90deg, #f39c12, #e67e22);
}

.progress-fill.danger {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 0.9rem;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.loading {
  text-align: center;
  color: #7f8c8d;
  font-style: italic;
  padding: 2rem;
}

.error {
  color: #e74c3c;
  background: #fdf2f2;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #e74c3c;
  margin: 1rem 0;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  text-decoration: none;
  margin-bottom: 1rem;
  font-weight: 600;
}

.back-link:hover {
  text-decoration: underline;
}

.chart-container {
  height: 300px;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
  color: #7f8c8d;
}

.full-width {
  grid-column: 1 / -1;
}

@media (max-width: 768px) {
  .performance-container {
    padding: 1rem;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .controls-grid {
    grid-template-columns: 1fr;
  }
}
</style>

<div class="performance-container">
  <a href="/manager" class="back-link">
    ← Back to Manager Dashboard
  </a>

  <div class="page-header">
    <h1>Team Performance Analytics</h1>
    <p>Monitor team performance, completion rates, and identify improvement opportunities</p>
  </div>

  <!-- Controls Section -->
  <div class="controls-section">
    <div class="controls-grid">
      <div class="form-group">
        <label for="periodSelect">Time Period</label>
        <select id="periodSelect" class="form-control">
          <option value="7">Last 7 days</option>
          <option value="30" selected>Last 30 days</option>
          <option value="90">Last 90 days</option>
        </select>
      </div>
      
      <div class="form-group">
        <label for="teamFilter">Team Filter</label>
        <select id="teamFilter" class="form-control">
          <option value="">All Teams</option>
        </select>
      </div>
      
      <div class="form-group">
        <label>&nbsp;</label>
        <button id="refreshBtn" class="btn btn-primary">
          🔄 Refresh Data
        </button>
      </div>
    </div>
  </div>

  <!-- Analytics Grid -->
  <div class="analytics-grid">
    <!-- Team Performance Overview -->
    <div class="analytics-card">
      <h3 class="card-title">📊 Team Performance Overview</h3>
      <div id="teamPerformanceContent" class="loading">
        Loading team performance data...
      </div>
    </div>

    <!-- Completion Trends -->
    <div class="analytics-card">
      <h3 class="card-title">📈 Completion Trends</h3>
      <div id="completionTrendsContent" class="loading">
        Loading completion trends...
      </div>
    </div>

    <!-- Compliance Metrics -->
    <div class="analytics-card full-width">
      <h3 class="card-title">✅ Compliance & Quality Metrics</h3>
      <div id="complianceMetricsContent" class="loading">
        Loading compliance metrics...
      </div>
    </div>
  </div>
</div>

<script>
// Team performance analytics functionality
let backendApiUrl = 'http://localhost:3001';

document.addEventListener('DOMContentLoaded', function() {
    loadTeamFilters();
    loadPerformanceData();
    
    document.getElementById('refreshBtn').addEventListener('click', loadPerformanceData);
    document.getElementById('periodSelect').addEventListener('change', loadPerformanceData);
    document.getElementById('teamFilter').addEventListener('change', loadPerformanceData);
});

function getJWTToken() {
    return localStorage.getItem('jwtToken') || sessionStorage.getItem('jwtToken');
}

async function ensureAuthentication() {
    const token = getJWTToken();
    if (!token) {
        try {
            const response = await fetch('/api/auth/token', {
                method: 'POST',
                credentials: 'include'
            });

            if (response.ok) {
                const data = await response.json();
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    return true;
                }
            }
        } catch (error) {
            console.error('Failed to get JWT token:', error);
        }
        return false;
    }
    return true;
}

async function loadTeamFilters() {
    if (!(await ensureAuthentication())) return;

    const token = getJWTToken();
    
    try {
        const response = await fetch(`${backendApiUrl}/api/teams`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            const select = document.getElementById('teamFilter');
            
            // Keep "All Teams" option and add teams
            data.teams.forEach(team => {
                const option = document.createElement('option');
                option.value = team.team_id;
                option.textContent = team.team_name;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading team filters:', error);
    }
}

async function loadPerformanceData() {
    if (!(await ensureAuthentication())) return;

    const token = getJWTToken();
    const period = document.getElementById('periodSelect').value;
    
    // Load all performance data
    await Promise.all([
        loadTeamPerformance(token, period),
        loadComplianceMetrics(token, period),
        loadCompletionTrends(token, period)
    ]);
}

async function loadTeamPerformance(token, period) {
    try {
        const response = await fetch(`${backendApiUrl}/api/analytics/team-performance?days=${period}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            displayTeamPerformance(data.teamPerformance);
        } else {
            document.getElementById('teamPerformanceContent').innerHTML = '<div class="error">Failed to load team performance data</div>';
        }
    } catch (error) {
        console.error('Error loading team performance:', error);
        document.getElementById('teamPerformanceContent').innerHTML = '<div class="error">Error loading team performance</div>';
    }
}

async function loadComplianceMetrics(token, period) {
    try {
        const response = await fetch(`${backendApiUrl}/api/analytics/compliance?days=${period}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            displayComplianceMetrics(data.complianceMetrics);
        } else {
            document.getElementById('complianceMetricsContent').innerHTML = '<div class="error">Failed to load compliance metrics</div>';
        }
    } catch (error) {
        console.error('Error loading compliance metrics:', error);
        document.getElementById('complianceMetricsContent').innerHTML = '<div class="error">Error loading compliance metrics</div>';
    }
}

async function loadCompletionTrends(token, period) {
    try {
        const response = await fetch(`${backendApiUrl}/api/manager/team-performance?days=${period}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            displayCompletionTrends(data.performance);
        } else {
            document.getElementById('completionTrendsContent').innerHTML = '<div class="error">Failed to load completion trends</div>';
        }
    } catch (error) {
        console.error('Error loading completion trends:', error);
        document.getElementById('completionTrendsContent').innerHTML = '<div class="error">Error loading completion trends</div>';
    }
}

function displayTeamPerformance(teams) {
    const container = document.getElementById('teamPerformanceContent');
    
    if (teams.length === 0) {
        container.innerHTML = '<div class="loading">No team performance data available.</div>';
        return;
    }

    const tableHtml = `
        <table class="team-performance-table">
            <thead>
                <tr>
                    <th>Team</th>
                    <th>Size</th>
                    <th>Submissions</th>
                    <th>Active</th>
                    <th>Overdue</th>
                    <th>Avg Validation (hrs)</th>
                </tr>
            </thead>
            <tbody>
                ${teams.map(team => `
                    <tr>
                        <td><strong>${team.team_name}</strong></td>
                        <td>${team.team_size}</td>
                        <td>${team.submissions_last_30_days || 0}</td>
                        <td>${team.active_assignments || 0}</td>
                        <td style="color: ${team.overdue_assignments > 0 ? '#e74c3c' : '#27ae60'}">
                            ${team.overdue_assignments || 0}
                        </td>
                        <td>${team.avg_validation_turnaround_hours ? parseFloat(team.avg_validation_turnaround_hours).toFixed(1) : 'N/A'}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    container.innerHTML = tableHtml;
}

function displayComplianceMetrics(metrics) {
    const container = document.getElementById('complianceMetricsContent');
    
    if (metrics.length === 0) {
        container.innerHTML = '<div class="loading">No compliance data available for the selected period.</div>';
        return;
    }

    // Calculate overall metrics
    const totalSubmissions = metrics.reduce((sum, m) => sum + parseInt(m.total_submissions || 0), 0);
    const totalValidated = metrics.reduce((sum, m) => sum + parseInt(m.validated_submissions || 0), 0);
    const avgCompletion = metrics.reduce((sum, m) => sum + parseFloat(m.avg_completion_percentage || 0), 0) / metrics.length;
    const avgValidationSuccess = metrics.reduce((sum, m) => sum + parseFloat(m.avg_validation_success_percentage || 0), 0) / metrics.length;

    const metricsHtml = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
            <div>
                <div class="metric-value">${totalSubmissions}</div>
                <div class="metric-label">Total Submissions</div>
            </div>
            <div>
                <div class="metric-value">${totalValidated}</div>
                <div class="metric-label">Validated Submissions</div>
            </div>
            <div>
                <div class="metric-value">${avgCompletion.toFixed(1)}%</div>
                <div class="metric-label">Avg Completion Rate</div>
                <div class="progress-bar">
                    <div class="progress-fill ${avgCompletion < 70 ? 'danger' : avgCompletion < 85 ? 'warning' : ''}" 
                         style="width: ${avgCompletion}%"></div>
                </div>
            </div>
            <div>
                <div class="metric-value">${avgValidationSuccess.toFixed(1)}%</div>
                <div class="metric-label">Avg Validation Success</div>
                <div class="progress-bar">
                    <div class="progress-fill ${avgValidationSuccess < 80 ? 'danger' : avgValidationSuccess < 90 ? 'warning' : ''}" 
                         style="width: ${avgValidationSuccess}%"></div>
                </div>
            </div>
        </div>
        
        <h4>Recent Daily Metrics</h4>
        <table class="team-performance-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Checklist Type</th>
                    <th>Submissions</th>
                    <th>Completion %</th>
                    <th>Validation %</th>
                </tr>
            </thead>
            <tbody>
                ${metrics.slice(0, 10).map(metric => `
                    <tr>
                        <td>${new Date(metric.submission_date).toLocaleDateString()}</td>
                        <td>${metric.original_checklist_filename || 'Unknown'}</td>
                        <td>${metric.total_submissions}</td>
                        <td>${metric.avg_completion_percentage ? parseFloat(metric.avg_completion_percentage).toFixed(1) + '%' : 'N/A'}</td>
                        <td>${metric.avg_validation_success_percentage ? parseFloat(metric.avg_validation_success_percentage).toFixed(1) + '%' : 'N/A'}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    container.innerHTML = metricsHtml;
}

function displayCompletionTrends(performance) {
    const container = document.getElementById('completionTrendsContent');
    
    if (performance.length === 0) {
        container.innerHTML = '<div class="loading">No trend data available.</div>';
        return;
    }

    // Calculate trend metrics
    const totalTeams = performance.length;
    const teamsWithSubmissions = performance.filter(p => p.total_submissions > 0).length;
    const avgCompletionRate = performance.reduce((sum, p) => sum + parseFloat(p.completion_rate || 0), 0) / totalTeams;

    const trendsHtml = `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
            <div>
                <div class="metric-value">${teamsWithSubmissions}/${totalTeams}</div>
                <div class="metric-label">Active Teams</div>
            </div>
            <div>
                <div class="metric-value">${avgCompletionRate.toFixed(1)}%</div>
                <div class="metric-label">Avg Completion Rate</div>
            </div>
        </div>
        
        <div class="chart-container">
            📊 Detailed trend charts coming soon
        </div>
    `;

    container.innerHTML = trendsHtml;
}
</script>
