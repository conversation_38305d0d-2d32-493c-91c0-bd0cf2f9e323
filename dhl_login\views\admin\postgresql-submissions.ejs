<style>
  .submissions-page {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
  }

  .page-header {
    background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .page-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 300;
  }

  .submissions-container {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .submissions-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 2rem;
  }

  .submissions-table th,
  .submissions-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
  }

  .submissions-table th {
    background-color: #f5f5f5;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
  }

  .submissions-table tr:hover {
    background-color: #f9f9f9;
  }

  .submission-link {
    color: #2e7d32;
    text-decoration: none;
    font-weight: 600;
  }

  .submission-link:hover {
    text-decoration: underline;
  }

  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
  }

  .status-pending {
    background-color: #fff3cd;
    color: #856404;
  }

  .status-pendingsupervisorvalidation {
    background-color: #d1ecf1;
    color: #0c5460;
  }

  .status-supervisorvalidated {
    background-color: #d4edda;
    color: #155724;
  }

  .status-completed {
    background-color: #d4edda;
    color: #155724;
  }

  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
  }

  .pagination a,
  .pagination span {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    text-decoration: none;
    color: #333;
  }

  .pagination a:hover {
    background-color: #f5f5f5;
  }

  .pagination .current {
    background-color: #2e7d32;
    color: white;
    border-color: #2e7d32;
  }

  .pagination .disabled {
    color: #ccc;
    cursor: not-allowed;
  }

  .action-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .btn-primary {
    background-color: #2e7d32;
    color: white;
  }

  .btn-primary:hover {
    background-color: #1b5e20;
    color: white;
  }

  .btn-secondary {
    background-color: #6c757d;
    color: white;
  }

  .btn-secondary:hover {
    background-color: #545b62;
    color: white;
  }

  .no-data {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 3rem;
  }

  .filename-cell {
    font-family: monospace;
    font-size: 0.9rem;
    background-color: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
  }

  @media (max-width: 768px) {
    .submissions-page {
      padding: 1rem;
    }

    .page-header {
      padding: 1.5rem;
    }

    .page-header h1 {
      font-size: 2rem;
    }

    .submissions-table {
      font-size: 0.9rem;
    }

    .submissions-table th,
    .submissions-table td {
      padding: 0.5rem;
    }

    .action-buttons {
      flex-direction: column;
    }
  }
</style>

<div class="submissions-page">
  <div class="page-header">
    <h1>📋 Checklist Submissions</h1>
    <p>All checklist submissions stored in PostgreSQL database</p>
  </div>

  <div class="action-buttons">
    <a href="/admin/postgresql" class="btn btn-secondary">← Back to PostgreSQL Dashboard</a>
    <a href="/admin" class="btn btn-secondary">← Back to Admin Dashboard</a>
  </div>

  <div class="submissions-container">
    <% if (submissions && submissions.length > 0) { %>
      <table class="submissions-table">
        <thead>
          <tr>
            <th>ID</th>
            <th>Checklist Title</th>
            <th>Original Filename</th>
            <th>Submitted By</th>
            <th>Submission Date</th>
            <th>Status</th>
            <th>JSON File</th>
          </tr>
        </thead>
        <tbody>
          <% submissions.forEach(submission => { %>
            <tr>
              <td>
                <a href="/admin/postgresql/submissions/<%= submission.submission_id %>" class="submission-link">
                  <%= submission.submission_id %>
                </a>
              </td>
              <td><%= submission.checklist_title %></td>
              <td>
                <% if (submission.original_checklist_filename) { %>
                  <span class="filename-cell"><%= submission.original_checklist_filename %></span>
                <% } else { %>
                  <em>N/A</em>
                <% } %>
              </td>
              <td><%= submission.submitted_by_username || 'Unknown' %></td>
              <td><%= new Date(submission.submission_timestamp).toLocaleString() %></td>
              <td>
                <span class="status-badge status-<%= submission.status.toLowerCase().replace(/\s+/g, '') %>">
                  <%= submission.status %>
                </span>
              </td>
              <td>
                <% if (submission.json_file_path) { %>
                  <span class="filename-cell"><%= submission.json_file_path.split('/').pop() %></span>
                <% } else { %>
                  <em>N/A</em>
                <% } %>
              </td>
            </tr>
          <% }); %>
        </tbody>
      </table>

      <!-- Pagination -->
      <div class="pagination">
        <% if (hasPrevPage) { %>
          <a href="?page=<%= currentPage - 1 %>">← Previous</a>
        <% } else { %>
          <span class="disabled">← Previous</span>
        <% } %>

        <span class="current">Page <%= currentPage %> of <%= totalPages %></span>

        <% if (hasNextPage) { %>
          <a href="?page=<%= currentPage + 1 %>">Next →</a>
        <% } else { %>
          <span class="disabled">Next →</span>
        <% } %>
      </div>
    <% } else { %>
      <div class="no-data">
        <h3>No submissions found</h3>
        <p>No checklist submissions have been recorded in the PostgreSQL database yet.</p>
      </div>
    <% } %>
  </div>
</div>
