

<style>
.assignments-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.page-header {
  background: rgba(255, 255, 255, 0.95);
  padding: 2rem;
  border-radius: 15px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.page-header h1 {
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.assignment-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.assignments-list {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-weight: 600;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: #ecf0f1;
  color: #2c3e50;
}

.btn-secondary:hover {
  background: #d5dbdb;
}

.assignment-item {
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: box-shadow 0.3s ease;
}

.assignment-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.assignment-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.assignment-title {
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status-assigned {
  background: #e3f2fd;
  color: #1976d2;
}

.status-inprogress {
  background: #fff3e0;
  color: #f57c00;
}

.status-completed {
  background: #e8f5e8;
  color: #388e3c;
}

.status-cancelled {
  background: #ffebee;
  color: #d32f2f;
}

.assignment-details {
  color: #7f8c8d;
  font-size: 0.9rem;
  line-height: 1.4;
}

.loading {
  text-align: center;
  color: #7f8c8d;
  font-style: italic;
  padding: 2rem;
}

.error {
  color: #e74c3c;
  background: #fdf2f2;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #e74c3c;
  margin: 1rem 0;
}

.success {
  color: #27ae60;
  background: #f0fff4;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #27ae60;
  margin: 1rem 0;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  text-decoration: none;
  margin-bottom: 1rem;
  font-weight: 600;
}

.back-link:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .assignments-container {
    padding: 1rem;
  }

  .content-grid {
    grid-template-columns: 1fr;
  }
}
</style>

<div class="assignments-container">
  <a href="/manager" class="back-link">
    ← Back to Manager Dashboard
  </a>

  <div class="page-header">
    <h1>Manual Assignments</h1>
    <p>Create and manage manual checklist assignments for your team members</p>
  </div>

  <div class="content-grid">
    <!-- Assignment Form -->
    <div class="assignment-form">
      <h3>Create New Assignment</h3>
      <form id="assignmentForm">
        <div class="form-group">
          <label for="checklistSelect">Checklist Type</label>
          <select id="checklistSelect" class="form-control" required>
            <option value="">Loading checklists...</option>
          </select>
        </div>

        <div class="form-group">
          <label for="assignedUserId">Assign To (User ID)</label>
          <input type="text" id="assignedUserId" class="form-control" required 
                 placeholder="Enter user ID">
        </div>

        <div class="form-group">
          <label for="teamSelect">Team (Optional)</label>
          <select id="teamSelect" class="form-control">
            <option value="">Loading teams...</option>
          </select>
        </div>

        <div class="form-group">
          <label for="dueDate">Due Date (Optional)</label>
          <input type="datetime-local" id="dueDate" class="form-control">
        </div>

        <div class="form-group">
          <label for="notes">Notes (Optional)</label>
          <textarea id="notes" class="form-control" rows="3" 
                    placeholder="Additional instructions or notes"></textarea>
        </div>

        <button type="submit" class="btn btn-primary">
          ➕ Create Assignment
        </button>
      </form>

      <div id="formMessage"></div>
    </div>

    <!-- Assignments List -->
    <div class="assignments-list">
      <h3>Recent Assignments</h3>
      <div id="assignmentsList" class="loading">
        Loading assignments...
      </div>
    </div>
  </div>
</div>

<script>
// Manual assignments functionality
let backendApiUrl = 'http://localhost:3001';

document.addEventListener('DOMContentLoaded', function() {
    loadChecklists();
    loadTeams();
    loadAssignments();
    
    document.getElementById('assignmentForm').addEventListener('submit', handleFormSubmit);
});

function getJWTToken() {
    return localStorage.getItem('jwtToken') || sessionStorage.getItem('jwtToken');
}

async function ensureAuthentication() {
    const token = getJWTToken();
    if (!token) {
        try {
            const response = await fetch('/api/auth/token', {
                method: 'POST',
                credentials: 'include'
            });

            if (response.ok) {
                const data = await response.json();
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    return true;
                }
            }
        } catch (error) {
            console.error('Failed to get JWT token:', error);
        }
        return false;
    }
    return true;
}

async function loadChecklists() {
    if (!(await ensureAuthentication())) return;

    const token = getJWTToken();
    
    try {
        const response = await fetch(`${backendApiUrl}/api/manager/available-checklists`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            const select = document.getElementById('checklistSelect');
            
            select.innerHTML = '<option value="">Select a checklist type</option>';
            data.checklists.forEach(checklist => {
                const option = document.createElement('option');
                option.value = JSON.stringify({
                    filename: checklist.original_checklist_filename,
                    title: checklist.checklist_title
                });
                option.textContent = `${checklist.checklist_title} (${checklist.usage_count} uses)`;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading checklists:', error);
    }
}

async function loadTeams() {
    if (!(await ensureAuthentication())) return;

    const token = getJWTToken();
    
    try {
        const response = await fetch(`${backendApiUrl}/api/teams`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            const select = document.getElementById('teamSelect');
            
            select.innerHTML = '<option value="">No team assignment</option>';
            data.teams.forEach(team => {
                const option = document.createElement('option');
                option.value = team.team_id;
                option.textContent = team.team_name;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading teams:', error);
    }
}

async function loadAssignments() {
    if (!(await ensureAuthentication())) return;

    const token = getJWTToken();
    
    try {
        const response = await fetch(`${backendApiUrl}/api/manager/manual-assignments`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            displayAssignments(data.assignments);
        } else {
            document.getElementById('assignmentsList').innerHTML = '<div class="error">Failed to load assignments</div>';
        }
    } catch (error) {
        console.error('Error loading assignments:', error);
        document.getElementById('assignmentsList').innerHTML = '<div class="error">Error loading assignments</div>';
    }
}

function displayAssignments(assignments) {
    const container = document.getElementById('assignmentsList');
    
    if (assignments.length === 0) {
        container.innerHTML = '<div class="loading">No assignments found.</div>';
        return;
    }

    const assignmentsHtml = assignments.map(assignment => {
        const statusClass = `status-${assignment.status.toLowerCase().replace(/\s+/g, '')}`;
        const dueDate = assignment.due_timestamp ? new Date(assignment.due_timestamp).toLocaleDateString() : 'No due date';
        
        return `
            <div class="assignment-item">
                <div class="assignment-header">
                    <h4 class="assignment-title">${assignment.checklist_title}</h4>
                    <span class="status-badge ${statusClass}">${assignment.status}</span>
                </div>
                <div class="assignment-details">
                    <strong>Assigned to:</strong> ${assignment.assigned_to_user_id}<br>
                    <strong>Due:</strong> ${dueDate}<br>
                    <strong>Team:</strong> ${assignment.team_name || 'No team'}<br>
                    <strong>Created:</strong> ${new Date(assignment.assignment_timestamp).toLocaleDateString()}
                    ${assignment.assignment_notes ? `<br><strong>Notes:</strong> ${assignment.assignment_notes}` : ''}
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = assignmentsHtml;
}

async function handleFormSubmit(event) {
    event.preventDefault();
    
    if (!(await ensureAuthentication())) return;

    const token = getJWTToken();
    const messageDiv = document.getElementById('formMessage');
    
    // Get form data
    const checklistData = JSON.parse(document.getElementById('checklistSelect').value);
    const assignedUserId = document.getElementById('assignedUserId').value;
    const teamId = document.getElementById('teamSelect').value || null;
    const dueDate = document.getElementById('dueDate').value || null;
    const notes = document.getElementById('notes').value || null;
    
    try {
        const response = await fetch(`${backendApiUrl}/api/manager/manual-assignment`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                assignedUserId,
                checklistFilename: checklistData.filename,
                checklistTitle: checklistData.title,
                dueDate,
                notes,
                teamId
            })
        });

        if (response.ok) {
            messageDiv.innerHTML = '<div class="success">Assignment created successfully!</div>';
            document.getElementById('assignmentForm').reset();
            loadAssignments(); // Refresh the list
        } else {
            const error = await response.json();
            messageDiv.innerHTML = `<div class="error">Failed to create assignment: ${error.message}</div>`;
        }
    } catch (error) {
        console.error('Error creating assignment:', error);
        messageDiv.innerHTML = '<div class="error">Error creating assignment. Please try again.</div>';
    }
}
</script>
